@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'IBM Plex Sans Arabic', sans-serif;
    direction: rtl;
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-arabic text-gray-800 bg-white;
    line-height: 1.7;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-8 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply bg-transparent border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white font-semibold py-3 px-8 rounded-2xl transition-all duration-300;
  }
  
  .section-padding {
    @apply py-20 px-4 sm:px-6 lg:px-8;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto;
  }
  
  .card-hover {
    @apply transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-500 to-primary-700 bg-clip-text text-transparent;
  }
  
  .floating-element {
    @apply animate-float;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .backdrop-blur-custom {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #00BFA6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00a693;
}

/* Locomotive Scroll */
html.has-scroll-smooth {
  overflow: hidden;
}

html.has-scroll-dragging {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.has-scroll-smooth body {
  overflow: hidden;
}

.has-scroll-smooth [data-scroll-container] {
  min-height: 100vh;
}

/* Animation delays for staggered effects */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
